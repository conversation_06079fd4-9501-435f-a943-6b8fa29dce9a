/* Общие стили */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 1440px;
    height: 1080px;
    margin: 0 auto;
    background-color: #f5f5f5;
}

/* Header стили */
.main-header {
    width: 100%;
    height: 90px;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}

.header-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
}

/* Логотип секция */
.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    width: 85px;
    height: 85px;
    object-fit: contain;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.logo-title {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    letter-spacing: -0.5px;
}

.logo-subtitle {
    font-size: 24px;
    font-weight: 400;
    color: #2c3e50;
    letter-spacing: -0.5px;
}

/* Правая часть header */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Кнопка "Купить билет" */
.buy-ticket-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #f39c12;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.buy-ticket-btn:hover {
    background-color: #e67e22;
}

.ticket-icon {
    font-size: 16px;
}

/* Кнопка поиска */
.search-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: #f8f9fa;
}

.search-icon {
    font-size: 18px;
    color: #6c757d;
}

/* Селектор языка */
.language-selector {
    position: relative;
}

.language-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    background: none;
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.language-btn:hover {
    background-color: #f8f9fa;
}

.dropdown-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
}

.language-selector:hover .dropdown-arrow {
    transform: rotate(180deg);
}

/* Выпадающее меню языков */
.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 80px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.language-selector:hover .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: block;
    padding: 8px 12px;
    color: #2c3e50;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.language-option:hover {
    background-color: #f8f9fa;
}

.language-option.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

/* Адаптивность для больших экранов */
@media (min-width: 1441px) {
    body {
        width: 100%;
        max-width: 1440px;
    }
}

/* Основной контент */
main {
    width: 100%;
    min-height: calc(1080px - 90px);
    padding: 20px 40px;
}
