<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4fae3adf-e702-4752-b77b-6ab9289587c1" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/Gorki_rework.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/requirements/my_requirements.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/config/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/config/asgi.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/config/settings.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/config/urls.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/config/wsgi.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/css/hp-fragments/footer.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/css/hp-fragments/header.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/css/hp-fragments/hero.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/css/hp-fragments/museum.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/js/hp-fragments/footer.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/js/hp-fragments/header.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/js/hp-fragments/hero.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/static/js/hp-fragments/museum.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/base.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/homepage.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/hp-fragments/footer.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/hp-fragments/header.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/hp-fragments/hero.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/core/templates/hp-fragments/museum_card.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/manage.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31WC2KiHlqlqCHlmcGS0srN7lam" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DefaultHtmlFileTemplate": "HTML File",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.manage.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "settings.editor.selected.configurable": "terminal"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-d9fa9deec7cb-d902c0275401-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-252.23892.515" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4fae3adf-e702-4752-b77b-6ab9289587c1" name="Changes" comment="" />
      <created>1755630232108</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755630232108</updated>
    </task>
    <servers />
  </component>
</project>